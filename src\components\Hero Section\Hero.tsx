"use client";
import React from 'react'

import HeroShapes from './HeroShapes'
import BestQualityAssurance from './BestQualityAssurance';
import Button5 from './EnhancedButtons';

const Hero = () => {
  return (
    <div className="relative bg-white w-full">
      {/* Large Orange Circle - moved to root level to act as background for both sections */}
      <div className="absolute left-[50%] top-[96vh] -translate-x-1/2 -translate-y-1/2 w-[60rem] h-[60rem] bg-[#F9B87B] rounded-full z-0 opacity-95"></div>

      {/* Main Section */}
      <section className="relative h-[100vh] w-full flex flex-col items-center justify-center pt-32 z-10">

        {/* Hero floating shapes */}
        <HeroShapes />

        {/* Headings */}
        <h1 className="relative z-10 text-[15rem] leading-none font-serif font-extrabold text-[#0F1117] tracking-tight select-none -mt-30" style={{fontFamily: 'Georgia, serif'}}>PerPixel</h1>
        <h2 className="relative z-10 mt-[-1.5rem] text-[12rem] leading-none font-serif font-extrabold text-white tracking-tight select-none" style={{fontFamily: 'Georgia, serif'}}>Agency</h2>

        {/* Enhanced Buttons */}
        <Button5 />

        {/* Right Quote (moved to right, vertical alignment) */}
        <div className="absolute right-12 top-2/3 w-[320px] z-10 flex flex-col items-start">
          <span className="text-[5rem] text-[#23283B] leading-none -mb-6">“</span>
          <span className="text-black text-3xl font-medium leading-tight">Our team's exceptional product design ensure website’s success.</span>
        </div>

        {/* Left Testimonial, Rating, Experience (moved to left) */}
        <div className="absolute left-4 top-1/3 z-10">
          <BestQualityAssurance />
        </div>
      </section>
    </div>
  )
}

export default Hero