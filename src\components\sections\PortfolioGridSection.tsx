"use client";
import React from "react";
import { motion } from "framer-motion";

const PortfolioGridSection = () => {
  return (
    <div className="relative w-full overflow-hidden bg-white">
      {/* Circle decoration with absolute positioning */}
      <div 
        className="absolute right-[15%] bottom-[15%] w-[300px] h-[300px] opacity-20 pointer-events-none" 
        style={{ zIndex: 0 }}
      >
        <img 
          src="/img/PER PIXEL_Layout-Vector.png" 
          alt="Decorative circle" 
          className="w-full h-full object-contain" 
        />
      </div>
      
      {/* Main section content with higher z-index */}
      <section className="relative w-full px-6 py-16" style={{ zIndex: 10 }}
    >
      
      {/* Desktop Grid */}
      <div
        className="portfolio-grid w-full max-w-[1400px] mx-auto hidden lg:grid relative"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(12, 1fr)',
          gridTemplateRows: 'repeat(6, minmax(140px, 1fr))',
          gridTemplateAreas: [
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"social-links social-links social-links social-links . . . . . . . ."'
          ].join(' '),
          gap: '16px',
          minHeight: '800px',
          position: 'relative',
          zIndex: 10
        }}
      >
        {/* Artist Redefining Architecture Card */}
        <div style={{ gridArea: 'slogan-intro' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[40px] text-[#000]">AI-Driven Design</div>
          {/* Decorative flower pattern from public/img */}
          <div className="absolute top-4 right-4 w-12 h-12 opacity-40">
            <img src="/img/AI-Driven_Card-Vector.png" alt="Decorative flower" className="w-full h-full object-contain" />
          </div>
        </div>

        {/* Portrait Card */}
        <div style={{ gridArea: 'portrait' }} className="rounded-[20px] min-h-[120px] overflow-hidden">
          <img src="/img/layput_card-2.png" alt="Portrait" className="w-full h-full object-cover" />
        </div>

        {/* Musea Project Card */}
        <div style={{ gridArea: 'work' }} className="bg-[#fadcd9] flex flex-col justify-start items-start p-6 rounded-[20px] min-h-[120px] relative">
          <div className="flex justify-between items-start w-full mb-3">
            <h3 className="font-['Gilroy:Medium',_sans-serif] text-[25px] text-[#000]">Musea</h3>
            <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
          
          {/* Single large image */}
          <div className="w-full mb-3">
            <img src="/img/layput_card-3.png" alt="Musea Project" className="rounded-lg w-full h-32 object-cover" />
          </div>
          
          {/* Project names in vertical list */}
          <div className="space-y-1 mb-3">
            <div className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Elara</div>
            <div className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Verve</div>
            <div className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Zephyr</div>
          </div>
          
          <div className="mt-auto">
            <div className="font-['Gilroy:Medium',_sans-serif] text-[14px] text-[#000]">Social Projects</div>
            <div className="text-[12px] text-[#000]">Follow our journey</div>
            <div className="flex gap-3 mt-1 text-[12px] text-[#000]">
              <span>INSTAGRAM</span>
              <span>TWITTER</span>
              <span>LINKEDIN</span>
            </div>
          </div>
        </div>

        {/* PER PIXEL About Card */}
        <div style={{ gridArea: 'about' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative">
          <div className="font-['Gilroy:Black',_sans-serif] text-[48px] leading-[52px] text-[#000] mb-4 font-black">PER PIXEL</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[20px] text-[#000] leading-relaxed">
            Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.
          </div>
        </div>

        {/* Contact Card */}
        <div style={{ gridArea: 'contact' }} className="bg-[#f8afa6] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[55px] text-[#000] mb-2">Contact me</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[15px] text-[#000] mb-2">Have some questions?</div>
          {/* X icon in top right corner */}
          <div className="absolute top-4 right-4 cursor-pointer hover:opacity-70 transition-opacity">
            <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
        </div>

        {/* Social Links Card */}
        <div style={{ gridArea: 'social-links' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[14px] text-[#000] uppercase space-y-2">
            <div className="cursor-pointer hover:opacity-70 transition-opacity">INSTAGRAM</div>
            <div className="cursor-pointer hover:opacity-70 transition-opacity">TWITTER</div>
            <div className="cursor-pointer hover:opacity-70 transition-opacity">LINKEDIN</div>
          </div>
        </div>
      </div>

      {/* Tablet Grid - Simplified responsive version */}
      <div className="hidden md:grid lg:hidden grid-cols-6 gap-4 w-full max-w-[900px] mx-auto relative" style={{ zIndex: 10 }}>
        <div className="col-span-3 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[36px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[36px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">AI-Driven Design</div>
        </div>
        <div className="col-span-3 bg-white p-6 rounded-[20px] flex justify-center">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg max-w-[150px] object-cover" />
        </div>
        <div className="col-span-2 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] text-[#000]">PER PIXEL</div>
        </div>
        <div className="col-span-2 bg-[#f8afa6] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] text-[#000]">Contact me</div>
        </div>
        <div className="col-span-2 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[20px] text-[#000]">Musea</div>
        </div>
      </div>

      {/* Mobile Grid - Stack vertically */}
      <div className="grid md:hidden grid-cols-1 gap-4 w-full max-w-[400px] mx-auto relative" style={{ zIndex: 10 }}>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[20px] leading-[24px] text-[#000]">AI-Driven Design</div>
        </div>
        <div className="bg-white p-6 rounded-[20px] flex justify-center">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg max-w-[120px] object-cover" />
        </div>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] text-[#000]">PER PIXEL</div>
        </div>
        <div className="bg-[#f8afa6] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] text-[#000]">Contact me</div>
        </div>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[18px] text-[#000]">Musea</div>
        </div>
      </div>
    </section>
    </div>
  );
};


export default PortfolioGridSection;
