"use client";
import React from "react";
import { motion } from "framer-motion";
import PortfolioCard from "../Portfolio/PortfolioCard";


const PortfolioGrid = () => {
  return (
    <section className="relative w-full px-6 py-16 overflow-hidden">
      {/* Desktop Grid */}
      <div
        className="portfolio-grid w-full max-w-[1400px] mx-auto hidden lg:grid relative"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(12, 1fr)',
          gridTemplateRows: 'repeat(6, minmax(140px, 1fr))',
          gridTemplateAreas: [
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"social-links social-links social-links social-links . . . . . . . ."'
          ].join(' '),
          gap: '16px',
          minHeight: '800px',
        }}
      >
        <div style={{ gridArea: 'slogan-intro' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Architecture</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[40px] text-[#000]">with AI-Driven Design</div>
        </div>
        <div style={{ gridArea: 'portrait' }} className="flex justify-center items-center bg-white rounded-[20px] min-h-[120px]">
          <img src="/img/layput_card-2.png" alt="Julia Huang" className="rounded-full w-32 h-32 object-cover" />
          <div className="ml-6 font-['Gilroy:Bold',_sans-serif] text-[28px] text-[#23283B]">Julia Huang</div>
        </div>
        <div style={{ gridArea: 'work' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[25px] text-[#000] mb-2">Musea</div>
          <img src="/img/layput_card-3.png" alt="Musea Project" className="rounded-lg w-full max-w-[200px] mb-2" />
          <div className="text-[#000] text-[18px] mb-1">Projects: Elara, Verve, Zephyr</div>
          <div className="mt-2">
            <div className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Social Projects</div>
            <div className="text-[14px] text-[#000]">Follow our journey</div>
            <div className="flex gap-2 mt-1">
              <span>Instagram</span>
              <span>LinkedIn</span>
              <span>Twitter</span>
            </div>
          </div>
        </div>
        <div style={{ gridArea: 'about' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Light',_sans-serif] text-[20px] text-[#000]">
            Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.
          </div>
        </div>
        <div style={{ gridArea: 'contact' }} className="bg-[#f8afa6] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[55px] text-[#000] mb-2">Contact me</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[15px] text-[#000] mb-2">Have some questions?</div>
          <div className="flex items-center gap-2">
            <span className="font-['Gilroy:Medium',_sans-serif] text-[16px] text-[#000]">Send an Email</span>
            <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M7 17L17 7M17 7H7M17 7V17" /></svg>
          </div>
        </div>
        <div style={{ gridArea: 'social-links' }} className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px]">
          <div className="font-['Gilroy:Light',_sans-serif] text-[15px] text-[#000] uppercase flex gap-4">
            <span>INSTAGRAM</span>
            <span>TWITTER</span>
            <span>LINKEDIN</span>
          </div>
        </div>
      </div>
      {/* Tablet and Mobile Grids can be similarly hardcoded if needed */}
    </section>
  );
}

export default PortfolioGrid;
